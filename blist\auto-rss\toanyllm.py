import requests
import os

# Set the API endpoint URLs
upload_url = 'https://llm.988886.xyz/api/v1/document/upload'
move_url = 'https://llm.988886.xyz/api/v1/document/move-files'

# Set the authorization token
headers = {
    'Authorization': 'Bearer 1ZZBYZ1-FQKMT66-MDC9YXK-Q69XE5N',
    'accept': 'application/json',
}

# Directories containing the files to upload and move
directories = ['/root/blist/auto-rss/publishembd', '/root/blist/auto-rss/starembd']

# Flag to check if any file was processed
files_processed = False

# Iterate over each directory
for directory_path in directories:
    # Iterate over each file in the directory
    for filename in os.listdir(directory_path):
        file_path = os.path.join(directory_path, filename)
        
        # Check if it is a file
        if os.path.isfile(file_path):
            # Upload the file
            with open(file_path, 'rb') as f:
                files = {'file': (filename, f, 'text/markdown')}  # Change to markdown
                response = requests.post(upload_url, headers=headers, files=files)
                response_data = response.json()
                
                # Check if the upload was successful
                if response_data['success']:
                    # Extract the location from the response
                    location = response_data['documents'][0]['location']
                    
                    # Prepare the data for moving the file
                    move_data = {
                        "files": [
                            {
                                "from": location,
                                "to": f"dailynews/{filename}-{location.split('-')[-1]}"
                            }
                        ]
                    }
                    
                    # Move the file
                    move_response = requests.post(move_url, headers={**headers, 'Content-Type': 'application/json'}, json=move_data)
                    
                    # Check if the move was successful
                    if move_response.status_code == 200:
                        print(f"Successfully moved {filename}")
                        files_processed = True
                    else:
                        print(f"Failed to move {filename}")
                else:
                    print(f"Failed to upload {filename}")

# Proceed only if files were processed
if files_processed:
    # API endpoint for getting the documents list
    get_documents_url = 'https://llm.988886.xyz/api/v1/documents'

    # API endpoint for updating the workspace embeddings
    update_embeddings_url = 'https://llm.988886.xyz/api/v1/workspace/news-46614857/update-embeddings'

    # Get the list of documents
    response1 = requests.get(get_documents_url, headers=headers)
    data = response1.json()

    # Initialize an empty list to hold the locations of the files in the news folder
    locations = []

    # Check if the 'localFiles' key is in the response data
    if 'localFiles' in data:
        # Iterate through the items in the 'localFiles' to find the 'news' folder
        for item in data['localFiles']['items']:
            if item['name'] == 'dailynews' and item['type'] == 'folder':
                # For each file in the 'dailynews' folder, construct the location and add it to the list
                for file in item['items']:
                    locations.append(f"dailynews/{file['name']}")

    # Prepare the data for the POST request to update the workspace embeddings
    update_data = {
        "adds": locations,
        "deletes": []
    }

    # Make the POST request to update the workspace embeddings
    update_response = requests.post(update_embeddings_url, headers={**headers, 'Content-Type': 'application/json'}, json=update_data)

    # Check the response
    if update_response.status_code == 200:
        print("Workspace updated successfully.")
    else:
        print("Failed to update the workspace.")
else:
    print("No files were processed. Skipping workspace update.")