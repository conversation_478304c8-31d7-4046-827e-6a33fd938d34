{"name": "blist", "version": "1.5.0", "description": "Blist is a clean and fast blog theme for your Hugo site.", "main": "index.js", "repository": "https://github.com/apvarun/blist-hugo-theme", "author": "Varun A P", "license": "MIT", "scripts": {"start": "hugo server --disable<PERSON>ast<PERSON><PERSON>", "start-examplesite": "hugo server -s exampleSite --themesDir=../.. --disableFastRender", "build-examplesite": "NODE_ENV=production hugo -s exampleSite --themesDir=../.. --gc", "build": "NODE_ENV=production hugo --gc && python3 url_replacer.py --config config.toml --public public", "build-vercel": "npm install && npm i -g postcss-cli && hugo --minify && python3 url_replacer.py --config config.toml --public public"}, "devDependencies": {"@tailwindcss/typography": "^0.4.0", "autoprefixer": "^10.2.5", "postcss": "^8.2.10", "postcss-cli": "^8.3.1", "postcss-import": "^14.0.1", "tailwindcss": "^2.1.1"}, "browserslist": ["last 1 version", "> 1%", "maintained node versions", "not dead"], "dependencies": {"axios": "^1.5.0", "cheerio": "^1.0.0-rc.12", "jsdom": "^24.0.0", "sanitize-filename": "^1.6.3", "xml2js": "^0.6.2"}}