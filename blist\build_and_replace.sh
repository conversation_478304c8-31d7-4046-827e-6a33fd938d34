#!/bin/bash

# Hugo Build and URL Replace Script
# This script builds the Hugo site and then replaces URLs in the generated files

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON> is installed
if ! command -v hugo &> /dev/null; then
    print_error "Hugo is not installed or not in PATH"
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python is not installed or not in PATH"
    exit 1
fi

# Determine Python command
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# Check if required Python packages are installed
print_info "Checking Python dependencies..."
if ! $PYTHON_CMD -c "import toml" 2>/dev/null; then
    print_warning "toml package not found. Installing..."
    $PYTHON_CMD -m pip install toml
fi

# Build the Hugo site
print_info "Building Hugo site..."
hugo --minify

if [ $? -ne 0 ]; then
    print_error "Hugo build failed"
    exit 1
fi

print_info "Hugo build completed successfully"

# Check if public directory exists
if [ ! -d "public" ]; then
    print_error "Public directory not found after Hugo build"
    exit 1
fi

# Run URL replacement
print_info "Starting URL replacement..."
$PYTHON_CMD url_replacer.py --config config.toml --public public

if [ $? -ne 0 ]; then
    print_error "URL replacement failed"
    exit 1
fi

print_info "URL replacement completed successfully"
print_info "Site build and URL replacement finished!"

# Optional: Show some statistics
if [ -d "public" ]; then
    file_count=$(find public -type f | wc -l)
    print_info "Generated site contains $file_count files"
fi
