---
thumbnail: https://wrhcn001.588886.xyz/webdav/files/uploads/ttrss/thumbnails/e6bb335a-3087-4046-86b5-f61d364916df.jpg
description: 西方媒体的报道常常带着有色眼镜看待中国，这一条有关网络安全的消息也不例外。  报道主要内容：据路透社获得的一封写给美国国会议员的信，本月早些时候，中国黑客利用第三方网络安全服务提供商BeyondTrust的漏洞，闯入美国财政部网络，窃取了一些非机密文件。目前没有证据表明该黑客组织仍可访问财政部系统。  评论：报道中，路透社直接以中国黑客入题，在没有确凿证据的情况下，推断黑客来自中国，带有明显偏见。另外，报道中没有提及具体的黑客组织名称，仅以中国黑客统称，也体现了西方媒体的刻板印象与懒惰。在网络安全领域，由于攻击技术的隐蔽性和溯源的复杂性，指认黑客来源需要严谨的证据链，而该报道中仅凭利用第三方网络安全服务提供商漏洞，以及中国黑客以往的攻击模式相似，就草率地做出判断，未免过于轻率和武断。该报道在证据链上仍存在很多疑点，有待调查澄清。同时，该报道也反映出中美两国在网络安全领域的博弈仍在持续，网络安全问题仍将是两国关系中的焦点之一。
tags: ["2024年12月"]
categories: ["2024年第53周"]
title: The Guardian-US Treasurys workstations breached in cyber-attack by China  report
date: 2024-12-30T21:35:07.195Z
---


<?xml encoding="UTF-8"><html><body><div><i>2024-12-30T21:00:23Z</i></div><div><img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/0f42d40ef4d8aba5d81678db7c529710250d5e97" alt="" referrerpolicy="no-referrer" loading="lazy">A federal building with exterior columns visible through a fence</div><p>A Chinese state-sponsored actor was behind a cyberattack on the US Treasury department earlier this month, resulting in unauthorized access to some of its workstations, Agence France-Presse reported on Monday, citing a letter to Congress.</p>
<p>The actor compromised a third-party cybersecurity service provider, BeyondTrust. The hackers were able to access workstations and some unclassified documents, but there is no evidence that the entity has continued access to the department&rsquo;s systems, the report said.</p>
<p>After being alerted by BeyondTrust, the Treasury department contacted the cybersecurity and infrastructure security agency and is collaborating with law enforcement to assess the impact.</p>
<p>The Treasury department and BeyondTrust did not immediately respond to Reuters requests for comment.</p><br><hr><div>&#33719;&#21462;&#26356;&#22810;RSS&#65306;<br><a href="https://feedx.net" target="_blank" rel="noopener noreferrer">https://feedx.net</a> <br><a href="https://feedx.site" target="_blank" rel="noopener noreferrer">https://feedx.site</a><br></div></body></html>
