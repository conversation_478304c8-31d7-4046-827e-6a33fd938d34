# coding = utf-8

import os
import datetime
import time

path = r'/root/blist/cliper/tiny-new'
today = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
today1 = time.strftime("%Y年%m月", time.localtime())
today2 = time.strftime("%Y年第%W周", time.localtime())
for root, dirs, files in os.walk(path, topdown=False):
    for name in files:
        _path = os.path.join(root, name)
        _name = name[:len(name)-3]
#        with open(_path, "r+", encoding="gbk") as f:
        with open(_path, 'r+') as f: 
#            old = f.read()
            data = f.readlines()
            data1=data[3:]
            data2=data[1]
            data3=data[2]
            data4=data2.split("](")[0]
            data5=data4[1:len(data4)]
            f.seek(0,0)
            f.write("---\n")
            f.write("pic: %s\n" %data5)
#            f.write("toc: true \n")
            f.write('tags: ["%s"]\n' %today1)
            f.write('categories: ["%s"]\n' %today2)
            f.write("desc: %s" %data3)
            f.write("title: %s\n" %_name)
            f.write("date: %sGMT+8\n" %today)
            f.write("---\n")
            f.writelines(data1)
