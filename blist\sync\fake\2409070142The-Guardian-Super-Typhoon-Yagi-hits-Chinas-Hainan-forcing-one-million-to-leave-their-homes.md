---
thumbnail: https://wrhsd001.588886.xyz/thumbnails/3977d65615b81bf9aa8b324083b807d4.png
description: 西方媒体的报道常常带有对中国的偏见，这篇关于台风八木的报道也一样。  报道主要内容：今年世界第二强的热带气旋八木袭击中国海南省，导致约100万人撤离，83万户停电。海南省以沙滩和豪华酒店闻名，在八木来临前已取消航班和渡轮，关闭商店和学校，并建议居民避免外出。此外，香港澳门广东和越南也受到影响，香港股市停市，学校停课。  评论：该报道虽提及中国政府和各地部门的应对措施，但总体基调仍偏向负面。报道过度强调台风的强度和破坏，而对中国政府有效措施的成果和受影响地区快速的恢复缺乏关注。此外，报道也忽略了气候变化对台风强度的影响，而这是科学家们已经指出的趋势。该报道延续了西方媒体对中国负面新闻的偏好，忽略了中国在面对自然灾害时的应急管理能力和有效措施。更客观的报道应包括对中国政府应对台风的措施和成果的介绍，以及对受影响地区快速恢复的关注。
tags: ["2024年09月"]
categories: ["2024年第36周"]
title: The Guardian-Super Typhoon Yagi hits Chinas Hainan forcing one million to leave their homes
date: 2024-09-07T01:42:05.981Z
---


<?xml encoding="UTF-8"><html><body><div><i>2024-09-07T01:18:32Z</i></div><div><img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/827108e84df85922c89da639bd13265488dc0e2b" alt="" referrerpolicy="no-referrer" loading="lazy">Residents walk past fallen trees amid strong winds and heavy rain as Typhoon Yagi makes landfall in Hainan province, China</div><p>Asia&rsquo;s strongest storm this year, Super Typhoon Yagi, landed in China&rsquo;s Hainan on Friday, bringing violent gales and heavy rain that triggered widespread power outages, paralysing the tourist island province and forcing about a million people in the country&rsquo;s south to leave their homes.</p>
<p>Packing maximum sustained winds of 234km/h near its centre, Yagi registers as the world&rsquo;s second-most powerful tropical cyclone so far this year &ndash; after the category-5 Atlantic Hurricane Beryl &ndash; and the most severe of 2024 in the Pacific basin.</p>
<p>After more than doubling in strength since killing 16 people in the northern Philippines earlier this week, Yagi slammed into the city of Wenchang in Hainan on Friday afternoon.</p>
<aside>
 <p><span>Related: </span><a href="https://www.theguardian.com/world/article/2024/aug/29/hyper-violent-typhoon-gaemi-climate-crisis-scientists" rel="noopener noreferrer" target="_blank">&lsquo;Hyper-violent&rsquo; Typhoon Gaemi was made fiercer by climate crisis, say scientists</a></p>
</aside>
<p>A little more than an hour after Yagi&rsquo;s arrival, Hainan saw power outages that affected 830,000 households in the province, the official news agency Xinhua said.</p>
<p>The provincial power supply department had put together a 7,000-member emergency team that would embark on repairs as soon as conditions permitted, Xinhua added. By Friday night, power to 260,000 households had been restored.</p>
<p>Ahead of Yagi&rsquo;s arrival, the island known for its sandy beaches and glitzy hotels had cancelled flights and ferries, shuttered businesses and told its population of more than 10 million to avoid going out.</p>
<figure>
 <img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/91943465e7013a8e719f5cbc4990bc730cd33ba7" alt="A pedestrian walks against wind on a street in Haikou in south China&rsquo;s Hainan province" referrerpolicy="no-referrer" loading="lazy">
 <figcaption>
  <span>A pedestrian walks against wind on a street in Haikou in south China&rsquo;s Hainan province.</span> <span>Photograph: Xinhua/REX/Shutterstock</span>
 </figcaption>
</figure>
<p>The typhoon had already shut schools, businesses and transport links in Hong Kong, Macau and Guangdong province as well as airports in Vietnam, which it is predicted to hit, along with Laos, over the weekend.</p>
<p>On Friday night, Yagi crossed Qiongzhou Strait north of Hainan and made its second landfall in Guangdong with winds still exceeding 200km/h. In Guangdong, more than 574,500 people had been evacuated from areas at risk by noon, more than two-thirds of them from the city of Zhanjiang.</p>
<p>In the financial hub of Hong Kong, the stock exchange was shuttered while schools remained closed.</p>
<p>Hong Kong&rsquo;s airport authority said operations had largely returned to normal after 50 flights were cancelled on Thursday, and the city of more than 7 million people also lowered its typhoon warning by a notch after midday, as Yagi moved west towards Vietnam.</p>
<p>The world&rsquo;s longest sea crossing, the main bridge linking Hong Kong with Macau and Zhuhai in Guangdong, also reopened on Friday afternoon after being shut since Thursday.</p>
<figure>
 <img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/84bccd60c666ead90475ed5fea341187903e5d9b" alt="Residents in the Philippines protect their belongings as they negotiate a flooded street caused by heavy rains from Tropical Storm Yagi" referrerpolicy="no-referrer" loading="lazy">
 <figcaption>
  <span>Residents in the Philippines protect their belongings as they negotiate a flooded street caused by heavy rains from Tropical Storm Yagi.</span> <span>Photograph: Aaron Favila/AP</span>
 </figcaption>
</figure>
<p>Yagi is the most severe storm to land in Hainan since 2014, when <a href="https://www.theguardian.com/world/2014/jul/20/typhoon-rammasun-china-dead-philippines" rel="noopener noreferrer" target="_blank">Typhoon Rammasun slammed into the island province</a> as a category-5 tropical cyclone. Rammasun killed 88 people in Hainan, Guangdong, Guangxi and Yunnan and caused economic losses of more than 44bn yuan ($6.25bn).</p>
<p>Formed over the warm seas east of the Philippines and following a similar path to Rammasun, Yagi arrived in China as a category-4 typhoon, ushering in winds strong enough to overturn vehicles, uproot trees and severely damage roads, bridges and buildings.</p>
<p>No fatalities have been reported so far in Hainan.</p>
<p>Typhoons are becoming stronger, fuelled by warmer oceans amid climate change, scientists say. Last week, <a href="https://www.theguardian.com/environment/article/2024/aug/30/weather-tracker-typhoon-shanshan-japan-rain" rel="noopener noreferrer" target="_blank">Typhoon Shanshan slammed into south-western Japan</a>, the strongest storm to hit the country in decades.</p><br><hr><div>&#33719;&#21462;&#26356;&#22810;RSS&#65306;<br><a href="https://feedx.net" target="_blank" rel="noopener noreferrer">https://feedx.net</a> <br><a href="https://feedx.run" target="_blank" rel="noopener noreferrer">https://feedx.run</a><br></div></body></html>
