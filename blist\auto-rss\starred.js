const { parseString } = require('xml2js');
exports.name = 'pythonclaude2';
const axios = require('axios');
const fs = require('fs');

const path = require('path');
const { promisify } = require('util');
const http = require('http');
const https = require('https');
const util = require('util');
const querystring = require('querystring');

const cheerio = require('cheerio');

const sanitizeFilename = require('sanitize-filename'); // You may need to install this package
const { exec } = require('child_process');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

const statAsync = util.promisify(fs.stat);
const unlinkAsync = promisify(fs.unlink);

const tokenFilePath = path.join(__dirname, 'kathystevens5.token');
let accessToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
//console.log(accessToken);

const username = '<EMAIL>'; // 替换成实际的用户名
const password = '#P#82zT#hs'; // 替换成实际的密码
//const baseUrl = 'https://api.wrss.tk';
const baseUrl = 'http://20.89.50.152:7999';

async function login() {
  try {
    const response = await axios.post(`${baseUrl}/chatgpt/login`, {
      username,
      password,
    }, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    console.log('accessToken已保存:', response.data.accessToken);
    fs.writeFileSync(tokenFilePath, response.data.accessToken, 'utf-8');
    return response.data.accessToken;
  } catch (error) {
    console.error('Error:', error.message);
  }
}

async function checkAccessToken() {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.get(`${baseUrl}/chatgpt/backend-api/accounts/check`, config);
    console.log('token正常:', response.data);
    // Process the list of available models as needed
  } catch (error) {
    console.error('账户错误或token过期', error);
    try {
      const newToken = await login();
      console.log('已更新token');
      const accessToken = newToken;
      // 在这里可以继续处理使用新的accessToken的逻辑
    } catch (loginError) {
      console.error('登录失败', loginError);
    }
  }  
}

async function getResponse(prompt) {
  let text = '随手搬运西方主流媒体的所谓的民主自由的报道，让帝国主义的丑恶嘴脸无处遁形。';
  // 请求数据
  await checkAccessToken();
  const gptToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
  const requestData = {
    action: 'next',
    messages: [
      {
        id: '97a0d360-2cc8-4842-b874-a9726dd7a7cb', // 请替换成唯一的消息ID
        author: {
          role: 'user'
        },
        content: {
          content_type: 'text',
          parts: [
            prompt
          ]
        },
        metadata: {}
      }
    ],
    model: 'text-davinci-002-render-sha',
    timezone_offset_min: -480,
    history_and_training_disabled: false
  };

  try {    
    const response = await axios.post(`${baseUrl}/chatgpt/backend-api/conversation`, requestData, {
      headers: {
        Authorization: `Bearer ${gptToken}`,
        'Content-Type': 'application/json',
        Accept: 'text/event-stream'
      }
    });
    // 处理响应
    let finishedResponses = [];

    response.data.split('\ndata:').forEach(dataChunk => {
      if (dataChunk.includes('"status": "finished_successfully"') && dataChunk.includes('"is_complete": true') && dataChunk.includes('"role": "assistant"')) {
        const contentMatch = /"content": {"content_type": "text", "parts": \["(.*?)"\]}/.exec(dataChunk);
        if (contentMatch && contentMatch[1]) {
          const content = contentMatch[1].replace(/\\u(\d{4})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16)));
          const decodedContent = JSON.parse('"' + content + '"'); // 解码Unicode转义字符
          finishedResponses.push(decodedContent);
        }
      }
    });
    
    if (finishedResponses.length > 0) {
      text = finishedResponses[0];
    }
    //console.log(text);
  } catch (error) {
    // 处理错误
    console.error('发生错误：', error);
  }
  console.log('返回内容：',text);
  return text; 
}

async function uploadImageToLsky(imageUrl) {
    const baseUrl = 'https://tu.988886.xyz/api/v1';
    const uploadEndpoint = '/upload';   
    const token = '3|z8icUcKgak6R52f5NF4ymGJqd8cGT43LD5R23FeX';
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
    };

    // 下载图片
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageData = Buffer.from(imageResponse.data, 'binary');
    
    // 构建FormData
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', imageData, 'image.png');
    
    try {
        // 上传图片
        const response = await axios.post(baseUrl + uploadEndpoint, form, { headers });

        if (response.status === 200) {
            const responseData = response.data;
            if (responseData.status) {
                const links = responseData.data.links;
                let url = links.url;
                let thumbnailUrl = links.thumbnail_url;
                
                // 替换URL中的特定部分
                url = url.replace('https://tu.988886.xyz/', 'https://wrhsd001.588886.xyz/');
                thumbnailUrl = thumbnailUrl.replace('https://tu.988886.xyz/', 'https://wrhsd001.588886.xyz/');                
                return { url, thumbnailUrl };
            } else {
                throw new Error('Image upload failed. Message: ' + responseData.message);
            }
        } else {
            throw new Error('Image upload request failed. Status code: ' + response.status);
        }
    } catch (error) {
        throw error;
    }
}

async function fetchAndSaveMarkdown(url, feedTitle, articleId, sessionId, acarticletitle) {
  try {
    

//保存txt文件
    const txtdecodedText = url.replace(/&amp;/g, '&');
    const txtencodedText = encodeURIComponent(txtdecodedText);
    const txtresponse = await fetch(txtdecodedText);    
    const htmlContent = await txtresponse.text();
    const $ = cheerio.load(htmlContent);
    const txtarticleTitle = acarticletitle;
    const txttitle = `${feedTitle}-${txtarticleTitle}`.replace(/[^\w\u4e00-\u9fa5\s-]/g, '');
    const txtsanitizedTitle = txttitle.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]+/g, '-');
    const txtarticleContent = $('article').text();
    const txtmarkdownText = `# ${txtsanitizedTitle}\n\n${txtarticleContent}`;
    const txtoutputPath = path.join(__dirname, "txt", `${txtsanitizedTitle}.md`);
    await fs.promises.writeFile(txtoutputPath, txtmarkdownText);
    console.log(`Markdown file saved as ${txtsanitizedTitle}.txt`);

//用id读取标签 全文内容
  const rssres = await fetch('https://ifeed.eu.org/tt-rss/api/', {
    method: 'POST',
    body: JSON.stringify({
      op: 'getArticle',
      sid: sessionId,
      article_id: articleId
    })  
  });

  const rssdata = await rssres.json();
  const article = rssdata.content[0];
  //const content = article.content;
  const content0 = article.content;
  const content = content0.replace(/https:\/\/ifeed\.eu\.org\/tt-rss\/public\.php\?op=cached/g, 'https://wrh.588886.xyz/tt-rss/public.php?op=cached');//这里是将ttrss的缓存的图片用cachefly进行cdn加速

  //console.log('New thumbnail URL:', content); // Debug

//用全文获取插件，文章时间，读取头图并上传图床  
    const scraperURL = 'https://quanwen.988886.xyz/parser';
    const encodedURL = encodeURIComponent(url);
    const newURL = `${scraperURL}?url=${encodedURL}`;
    const response = await axios.get(newURL);
    const articleData = response.data;
    const content1 = articleData.content;
    let orgtitle = article.title || new Date().toISOString(); // Use the article title or current time if no title is available
    if (!articleData.title) {
        console.log(`插件获取头图故障: ${url}`);
       // return; // Skip processing if title is missing
      }
    const title = `${feedTitle}-${orgtitle}`.replace(/[^\w\u4e00-\u9fa5\s-]/g, '');
    const sanitizedTitle = title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]+/g, '-');   
    
    let datePublished = new Date();
    if (articleData.date_published) {
        datePublished = new Date(articleData.date_published);
    }
    const year = datePublished.getFullYear();
    const month = datePublished.getMonth() + 1;

    // Calculate the week based on the difference between the datePublished and the start of the year
    const currentDate = new Date();
    const startOfYear = new Date(currentDate.getFullYear(), 0, 1); // January 1st of the current year
    const daysPassed = Math.floor((datePublished - startOfYear) / (24 * 60 * 60 * 1000)) + 1;
    const week = Math.ceil(daysPassed / 7);    
//文件名 插入时间
const titleyear = datePublished.getFullYear().toString().substr(2, 2); // 获取年份的后两位
const titlemonth = (datePublished.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
const titleday = datePublished.getDate().toString().padStart(2, '0'); // 获取日期并补零
const titlehours = datePublished.getHours().toString().padStart(2, '0'); // 获取小时并补零
const titleminutes = datePublished.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零

const titledata = `${titleyear}${titlemonth}${titleday}${titlehours}${titleminutes}`;
console.log(titledata); // 输出类似 '2309021900'


    const gptcontent = `你是一位观点犀利的新闻评论员，下面内容中的文字信息是一篇媒体的报道，请用150个左右的中文做出你对这篇报道的评论：${txtmarkdownText}`; //content是从ttrss读取的全文，congtent1是用插件用原链接获取的全文，txtmarkdownText是直接用网页获取的txt全文
    const gptdesc = await getResponse(gptcontent);
    const cleanedGptdesc = gptdesc.replace(/\n/g, ' ');
    
    let thumbnailUrl = 'https://wrhsd001.588886.xyz/thumbnails/cb6b93424cdac6d087679838f0b18e3f.png'; // Default thumbnail URL
    if (articleData.lead_image_url) {
      console.log('Original thumbnail URL:', articleData.lead_image_url); // Debug

      //const newImageUrl = await uploadImageToTelegraph(articleData.lead_image_url);.
      const newImageUrldata = await uploadImageToLsky(articleData.lead_image_url); //修改为使用wwxA1上的蓝空图床
      //const newImageUrl = newImageUrldata.url;
      const newImageUrl = newImageUrldata.thumbnailUrl; //修改为使用缩略图
      console.log('New thumbnail URL:', newImageUrl); // Debug

      if (newImageUrl) {
        thumbnailUrl = newImageUrl;
      }
    }

    const metadata = 
`---
thumbnail: ${thumbnailUrl}
description: ${cleanedGptdesc}
tags: ["${year}年${month < 10 ? '0' + month : month}月"]
categories: ["${year}年第${week}周"]
title: ${title}
date: ${datePublished.toISOString()}
---
`;

    const markdownContent = `${metadata}\n\n${content}`;   



if (!article.labels || article.labels.length === 0) {
  // 如果 labels 为空或没有内容，则保存到 draft 文件夹
  saveContent(`${sanitizedTitle}.md`, 'draft', markdownContent);
} else {
  // 如果 labels 不为空
  for (const label of article.labels) {
    const labelName = label[1]; // 获取 label 的名称
    saveContent(`${titledata}${sanitizedTitle}.md`, labelName, markdownContent);
  }
}

function saveContent(fileName, directory, content) {
  const savePath = path.join(__dirname, directory);
  
  // 创建保存目录（如果不存在）
  if (!fs.existsSync(savePath)) {
    fs.mkdirSync(savePath);
  }

  // 保存内容到文件

  const filePath = path.join(savePath, fileName);
  fs.writeFileSync(filePath, content);

  console.log(`Saved content to ${filePath}`);
}
    
    
    return `${sanitizedTitle}.md`;
  } catch (error) {
    console.error('获取全文或者生成md文件失败:', error);
    throw error;
  }
}

async function unstarArticle(sessionId, articleId) {

  const response = await fetch('https://ifeed.eu.org/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'updateArticle',
      sid: sessionId,
      article_ids: articleId,  
      mode: 0,
      field: 0
    })
  });
  
  const data = await response.json();

  return data;
}

async function loginAndGetStarred() {

  const loginRes = await fetch('https://ifeed.eu.org/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'login',
      user: 'admin',
      password: 'a881018'
    })
  });

  const loginData = await loginRes.json();

  const sessionId = loginData.content.session_id;

  const starredRes = await fetch('https://ifeed.eu.org/tt-rss/api/', {
    method: 'POST',
    body: JSON.stringify({
      op: 'getHeadlines',
      sid: sessionId,
      feed_id: '-1'
    })
  });

  const starredData = await starredRes.json();

for (let article of starredData.content) {
  console.log(article.id);
  console.log(article.title);
  console.log(article.link);
  console.log(article);
  console.log(article.feed_title);
  try {

      await fetchAndSaveMarkdown(article.link, article.feed_title, article.id, sessionId, article.title);
      await unstarArticle (sessionId, article.id);
      
  } catch (error) {
    console.error('Error processing RSS feed:', error);
  }
  }

}

loginAndGetStarred();