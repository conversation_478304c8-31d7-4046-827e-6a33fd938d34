const { parseString } = require('xml2js');
exports.name = 'pythonclaude2';
const axios = require('axios');
const fs = require('fs');

const path = require('path');
const { promisify } = require('util');
const http = require('http');
const https = require('https');
const util = require('util');
const querystring = require('querystring');

const cheerio = require('cheerio');

const sanitizeFilename = require('sanitize-filename'); // You may need to install this package
const { exec } = require('child_process');


const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

const statAsync = util.promisify(fs.stat);
const unlinkAsync = promisify(fs.unlink);

const tokenFilePath = path.join(__dirname, 'kathystevens5-.token');
let accessToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
//console.log(accessToken);

const username = '<EMAIL>'; // 替换成实际的用户名
const password = 'aA881018!@#$'; // 替换成实际的密码
const baseUrl = 'http://20.37.121.80:7999';
const basepandoraUrl = 'http://20.89.50.152:8181';




function gptfromapi(userContent) {
	
    const API_KEY = 'sk-1k2K44Fd1KSehZ0gD86237Fd2c5642C0AeD2E694F32d3f1b';
    
    const headers = {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
    }
    
    const data = {
        'model': 'cohere',
        'messages': [
            {
                'role': 'system',
                'content': 'system'
            },
            {
                'role': 'user',
                'content': userContent
            }        
        ]
    }

    return axios.post('http://146.56.99.144:7483/v1/chat/completions', data, {headers: headers})
        .then(response => {
            //console.log(response.data.choices[0]);
            return response.data.choices[0].message.content;
        })
        .catch(error => {
            console.error(error);
        });
}

async function login() {
  try {
    const response = await axios.post(`${baseUrl}/auth/token`, {
      username,
      password,
    }, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    console.log('accessToken已保存:', response.data.accessToken);
    fs.writeFileSync(tokenFilePath, response.data.accessToken, 'utf-8');
    return response.data.accessToken;
  } catch (error) {
    console.error('Error:', error.message);
    throw error;  // 在出错时抛出错误
  }
}

async function pandoralogin() {
  try {
    const response = await axios.post(`${basepandoraUrl}/wrhgpt123456/api/auth/login`, querystring.stringify({
    username: username,
    password: password,
  }), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    console.log('accessToken已保存:', response.data.access_token);
    fs.writeFileSync(tokenFilePath, response.data.access_token, 'utf-8');
    return response.data.access_token;
  } catch (error) {
    console.error('Error:', error.message);
    throw error;  // 在出错时抛出错误
  }
}


async function checkAccessToken() {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.get(`${baseUrl}/backend-api/accounts/check`, config);
    console.log('token正常:', response.data);
    // Process the list of available models as needed
  } catch (error) {
    console.error('账户错误或token过期', error);
    let attempts = 0;
    let success = false;
    while (attempts < 5 && !success) {
      try {
        const newToken = await login();
        console.log('已更新token');
        const accessToken = newToken;
        success = true;
        // 在这里可以继续处理使用新的accessToken的逻辑
      } catch (loginError) {
        console.error('登录失败，尝试使用pandoralogin更新token', loginError);
        try {
          const newToken = await pandoralogin();
          console.log('已更新token');
          const accessToken = newToken;
          return accessToken;
          success = true;
        } catch (pandoraLoginError) {
          console.error('pandoralogin更新token失败', pandoraLoginError);
        }
      }
      attempts++;
    }
    if (!success) {
      throw new Error('token更新失败');
    }
  }  
}


async function checkAccessTokengo() {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.get(`${baseUrl}/backend-api/accounts/check`, config);
    console.log('token正常:', response.data);
    // Process the list of available models as needed
  } catch (error) {
    console.error('账户错误或token过期', error);
    try {
      const newToken = await getAccessTokenFromGoProgram();
      await new Promise(resolve => setTimeout(resolve, 10000)); // go 程序获取token需要时间 等待10s
      console.log('已更新token');
      
    } catch (loginError) {
      console.error('登录失败', loginError);
    }
  }  
}
/*
async function checkAccessTokengo() {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.get(`${baseUrl}/backend-api/accounts/check`, config);
    console.log('token正常:', response.data);
    // Process the list of available models as needed
  } catch (error) {
    console.error('账户错误或token过期', error);
    try {
      const newToken = await getAccessTokenFromGoProgramrefresh();
      await new Promise(resolve => setTimeout(resolve, 10000)); // go 程序获取token需要时间 等待10s
      console.log('已更新token');
      
    } catch (loginError) {
      console.error('登录失败', loginError);
    }
  }  
}
*/
function getAccessTokenFromGoProgram() {
  const command = 'cd /root/blist/auto-rss/opaitokens && /usr/local/go/bin/go test -run TestUserCase'; // 替换为您的Go程序运行命令
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error('Error executing Go program:', error);
      return;
    }
    
    const accessToken = extractAccessToken(stdout); // 提取accessToken，根据实际输出格式进行调整
    console.log('Access Token from Go program:', accessToken);   
    fs.writeFileSync(tokenFilePath, accessToken, 'utf-8'); 
    return accessToken;
    // 在这里继续处理使用accessToken的逻辑
  });
}

function getAccessTokenFromGoProgramrefresh() {
  const command = 'cd /root/blist/auto-rss/opaitokens && /usr/local/go/bin/go test -run TestFakeOpenTokens_FetchAccessTokenBySessionToken'; // 替换为您的Go程序运行命令
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error('Error executing Go program:', error);
      return;
    }
    
      const outputLines = stdout.split('\n');
      const accessToken = outputLines[0].trim(); // 提取accessToken，根据实际输出格式进行调整

      
    console.log('Access Token from Go program:', accessToken);   
    fs.writeFileSync(tokenFilePath, accessToken, 'utf-8'); 
    return accessToken;
    // 在这里继续处理使用accessToken的逻辑
  });
}

function extractAccessToken(output) {
  const regex = /i am using access token: ([^\s]+)/;
  const match = output.match(regex);
  if (match && match[1]) {
    return match[1];
  } else {
    console.error('Access Token not found in output');
    return null;
  }
}

async function getResponse(prompt) {
  let text = '随手搬运西方主流媒体的所谓的民主自由的报道，让帝国主义的丑恶嘴脸无处遁形。';
  // 请求数据
  await checkAccessToken();
  
  //await checkAccessTokengo();
  //await new Promise((resolve) => setTimeout(resolve, 10000)); // go 程序获取token需要时间 等待10s
  const gptToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
  const requestData = {
    action: 'next',
    messages: [
      {
        id: '469d00af-6095-47b2-a9a0-8fec94b175d5', // 请替换成唯一的消息ID
        author: {
          role: 'user'
        },
        content: {
          content_type: 'text',
          parts: [
            prompt
          ]
        },
        metadata: {}
      }
    ],
    model: 'text-davinci-002-render-sha',
    parent_message_id: '8c875ca4-59c4-444d-920a-3a490d1f797d',
    timezone_offset_min: -480,
    history_and_training_disabled: false
  };

try {    
    let response;
    try {
      response = await axios.post(`${baseUrl}/backend-api/conversation`, requestData, {
        headers: {
          Authorization: `Bearer ${gptToken}`,
          'Content-Type': 'application/json',
          Accept: 'text/event-stream'
        }
      });
    } catch (error) {
      console.error('使用baseUrl发生错误，尝试使用baseUrl1：', error);
      response = await axios.post(`${basepandoraUrl}/wrhgpt123456/backend-api/conversation`, requestData, {
        headers: {
          Authorization: `Bearer ${gptToken}`,
          'Content-Type': 'application/json',
          Accept: 'text/event-stream'
        }
      });
    }

    // 处理响应
    let finishedResponses = [];
    console.log(response.data);
    response.data.split('\ndata:').forEach(dataChunk => {
      if (dataChunk.includes('"status": "finished_successfully"') && dataChunk.includes('"is_complete": true') && dataChunk.includes('"role": "assistant"')) {
        const contentMatch = /"content": {"content_type": "text", "parts": \["(.*?)"\]}/.exec(dataChunk);
        if (contentMatch && contentMatch[1]) {
          const content = contentMatch[1].replace(/\\u(\d{4})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16)));
          const decodedContent = JSON.parse('"' + content + '"'); // 解码Unicode转义字符
          finishedResponses.push(decodedContent);
        }
      }
    });
    
    if (finishedResponses.length > 0) {
      text = finishedResponses[0];
    }
    //console.log(text);
  } catch (error) {
    // 处理错误
    console.error('发生错误：', error);
  }
  console.log('返回内容：',text);
  return text; 
}

async function uploadImageToLsky222222(imageUrl) { //222222 原来的
    const baseUrl = 'https://tu.988886.xyz/api/v1';
    const uploadEndpoint = '/upload';   
    const token = '3|z8icUcKgak6R52f5NF4ymGJqd8cGT43LD5R23FeX';
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
    };

    // 下载图片
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageData = Buffer.from(imageResponse.data, 'binary');
    
    // 构建FormData
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', imageData, 'image.png');
    
    try {
        // 上传图片
        const response = await axios.post(baseUrl + uploadEndpoint, form, { headers });

        if (response.status === 200) {
            const responseData = response.data;
            if (responseData.status) {
                const links = responseData.data.links;
                let url = links.url;
                let thumbnailUrl = links.thumbnail_url;
                
                // 替换URL中的特定部分
                url = url.replace('https://tu.988886.xyz/', 'https://wrhsd001.588886.xyz/');
                thumbnailUrl = thumbnailUrl.replace('https://tu.988886.xyz/', 'https://wrhsd001.588886.xyz/');                
                return { url, thumbnailUrl };
            } else {
                throw new Error('Image upload failed. Message: ' + responseData.message);
            }
        } else {
            throw new Error('Image upload request failed. Status code: ' + response.status);
        }
    } catch (error) {
        throw error;
    }
}

async function uploadImageToLsky(imageUrl) {
    const baseUrl = 'https://xrea.988886.xyz/webdav';
    const uploadEndpoint = '/fixed-upload-new.php';   
    const headers = {
        'Content-Type': 'multipart/form-data',
    };

    // 下载图片
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageData = Buffer.from(imageResponse.data, 'binary');
    
    // 构建FormData
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', imageData, 'image.jpg');
    form.append('thumbnail', 'true');
    form.append('original', 'false');
    form.append('path', 'ttrss');
    
    try {
        // 上传图片
        const response = await axios.post(baseUrl + uploadEndpoint, form, { headers: { ...headers, ...form.getHeaders() } });

        if (response.status === 200) {
            const responseData = response.data;
            const thumbnailUrl = responseData.thumbnailResult;

            return { thumbnailUrl };
        } else {
            throw new Error('Image upload request failed. Status code: ' + response.status);
        }
    } catch (error) {
        throw error;
    }
}


function decodeHtmlCharCodes(str) {
  // 检查str是否未定义或不是一个字符串
  if (typeof str !== 'string') {
    // 返回原始输入或空字符串，根据你的需求选择
    return str || '';
  }

  return str.replace(/&#x([a-fA-F0-9]+);/g, function(match, dec) {
    return String.fromCharCode(parseInt(dec, 16));
  });
}

const { JSDOM } = require("jsdom");

// 确保JSDOM已经被定义
if (typeof JSDOM !== "undefined") {
  function extractTextFromHTML(htmlString) {
    // 检查htmlString是否为字符串
    if (typeof htmlString !== 'string') {
      // 如果不是字符串，返回空字符串或其他适当的默认值
      return '';
    }

    const dom = new JSDOM(htmlString);
    const document = dom.window.document;
    const window = dom.window;
    let text = "";
    const walk = document.createTreeWalker(document.body, window.NodeFilter.SHOW_TEXT, null, false);
    while(walk.nextNode()) {
      text += walk.currentNode.textContent + ' '; // 使用空格而不是换行符连接文本
    }
    return text.trim(); // 移除最后的空格
  }
} else {
  console.error("JSDOM is not defined. Please ensure you are running this in a Node.js environment with jsdom installed.");
}

async function fetchAndSaveMarkdown(url, feedTitle, articleId, sessionId, acarticletitle) {
  try {
    

//保存txt文件
    const txtdecodedText = url.replace(/&amp;/g, '&');
    const txtencodedText = encodeURIComponent(txtdecodedText);
    const txtresponse = await fetch(txtdecodedText);    
    const htmlContent = await txtresponse.text();
    const $ = cheerio.load(htmlContent);
    const txtarticleTitle = acarticletitle;
    const txttitle = `${feedTitle}-${txtarticleTitle}`.replace(/[^\w\u4e00-\u9fa5\s-]/g, '');
    const txtsanitizedTitle = txttitle.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]+/g, '-');
    const txtarticleContent = $('article').text();
    const txtmarkdownText = `# ${txtsanitizedTitle}\n\n${txtarticleContent}`;
    const txtoutputPath = path.join(__dirname, "txt", `${txtsanitizedTitle}.md`);
    await fs.promises.writeFile(txtoutputPath, txtmarkdownText);
    console.log(`Markdown file saved as ${txtsanitizedTitle}.txt`);

//用id读取标签 全文内容
  const rssres = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST',
    body: JSON.stringify({
      op: 'getArticle',
      sid: sessionId,
      article_id: articleId
    })  
  });

  const rssdata = await rssres.json();
  const article = rssdata.content[0];
  //const content = article.content;
  const content0 = article.content;
  const content = content0.replace(/https:\/\/ifeed\.eu\.org\/tt-rss\/public\.php\?op=cached/g, 'https://wrh.588886.xyz/tt-rss/public.php?op=cached');//这里是将ttrss的缓存的图片用cachefly进行cdn加速
  //    const decodedContent = decodeHtmlCharCodes(content);
 //     console.log('decodedContent:', decodedContent);
  //console.log('New thumbnail URL:', content); // Debug

//用全文获取插件，文章时间，读取头图并上传图床  
    const scraperURL = 'https://quanwen.988886.xyz/parser';
    const encodedURL = encodeURIComponent(url);
    const newURL = `${scraperURL}?url=${encodedURL}`;
    const response = await axios.get(newURL);
    const articleData = response.data;
    const content1 = articleData.content;
      const decodedContent1 = decodeHtmlCharCodes(content1);
//      console.log('decodedContent1:', decodedContent1);
      const extractedText1 = extractTextFromHTML(decodedContent1);
//      console.log('extractedText1:', extractedText1);
    let orgtitle = article.title || new Date().toISOString(); // Use the article title or current time if no title is available
    if (!articleData.title) {
        console.log(`插件获取头图故障: ${url}`);
       // return; // Skip processing if title is missing
      }
    const title = `${feedTitle}-${orgtitle}`.replace(/[^\w\u4e00-\u9fa5\s-]/g, '');
    const sanitizedTitle = title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]+/g, '-');   
    
    let datePublished = new Date();
/* 修改为系统时间，而不是发稿时间，时差的原因，导致新发的文章在面
    if (articleData.date_published) {
        datePublished = new Date(articleData.date_published);
    }
*/    
    const year = datePublished.getFullYear();
    const month = datePublished.getMonth() + 1;

    // Calculate the week based on the difference between the datePublished and the start of the year
    const currentDate = new Date();
    const startOfYear = new Date(currentDate.getFullYear(), 0, 1); // January 1st of the current year
    const daysPassed = Math.floor((datePublished - startOfYear) / (24 * 60 * 60 * 1000)) + 1;
    const week = Math.ceil(daysPassed / 7);    
//文件名 插入时间
const titleyear = datePublished.getFullYear().toString().substr(2, 2); // 获取年份的后两位
const titlemonth = (datePublished.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
const titleday = datePublished.getDate().toString().padStart(2, '0'); // 获取日期并补零
const titlehours = datePublished.getHours().toString().padStart(2, '0'); // 获取小时并补零
const titleminutes = datePublished.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零

const titledata = `${titleyear}${titlemonth}${titleday}${titlehours}${titleminutes}`;
console.log(titledata); // 输出类似 '2309021900'


    const gptcontent = `你是一位观点犀利的新闻评论员，崇尚客观公正，厌恶西方媒体对于中国充满偏见的报道。下面内容中的文字信息是一篇媒体的报道，请先简要总结主要内容，然后客观公正的评论这个可能充满偏见的报道，请注意，你的回复要用中文，回复的总字数要控制在200字左右：${txtmarkdownText}`;
   // const gptcontent = `你是一位观点犀利的新闻评论员，下面内容中的文字信息是一篇媒体的报道，请用150左右的文字做出你对这篇报道的评论，请注意，你的回复要用中文：${txtmarkdownText}`; //content是从ttrss读取的全文，congtent1是用插件用原链接获取的全文，txtmarkdownText是直接用网页获取的txt全文
    //const gptdesc = await getResponse(gptcontent);
    const gptdesc = await gptfromapi(gptcontent);
    //console.log('gptdesc:', gptdesc);
    
    const cleanedGptdesc = gptdesc
  ?.replace(/\n/g, ' ') // replace newline characters with space
  .replace(/[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\s\.,;?!，。？！；：‘’“”（）【】《》]/g, '');
    
    let thumbnailUrl = 'https://wrhsd001.588886.xyz/thumbnails/cb6b93424cdac6d087679838f0b18e3f.png'; // Default thumbnail URL
    if (articleData.lead_image_url) {
      console.log('Original thumbnail URL:', articleData.lead_image_url); // Debug

      //const newImageUrl = await uploadImageToTelegraph(articleData.lead_image_url);.
      const newImageUrldata = await uploadImageToLsky(articleData.lead_image_url); //修改为使用wwxA1上的蓝空图床
      //const newImageUrl = newImageUrldata.url;
      const newImageUrl = newImageUrldata.thumbnailUrl; //修改为使用缩略图
      console.log('New thumbnail URL:', newImageUrl); // Debug

      if (newImageUrl) {
        thumbnailUrl = newImageUrl;
      }
    }

    const metadata = 
`---
thumbnail: ${thumbnailUrl}
description: ${cleanedGptdesc}
tags: ["${year}年${month < 10 ? '0' + month : month}月"]
categories: ["${year}年第${week}周"]
title: ${title}
date: ${datePublished.toISOString()}
---
`;

    const markdownContent = `${metadata}\n\n${content}`;   



if (!article.labels || article.labels.length === 0) {
  // 如果 labels 为空或没有内容，则保存到 draft 文件夹
  const fileNamess = `${titledata}${sanitizedTitle}.md`.replace(/ /g, "_");
  saveContent(`${sanitizedTitle}.md`, 'draft', markdownContent);
  saveContent(fileNamess, 'starembd', extractedText1);
} else {
  // 如果 labels 不为空
  for (const label of article.labels) {
    const labelName = label[1]; // 获取 label 的名称
    const fileNamesss = `${titledata}${sanitizedTitle}.md`.replace(/ /g, "_");
    saveContent(`${titledata}${sanitizedTitle}.md`, labelName, markdownContent);
    saveContent(fileNamesss, 'starembd', extractedText1);
  }
}

function saveContent(fileName, directory, content) {
  const savePath = path.join(__dirname, directory);
  
  // 创建保存目录（如果不存在）
  if (!fs.existsSync(savePath)) {
    fs.mkdirSync(savePath);
  }

  // 保存内容到文件

  const filePath = path.join(savePath, fileName);
  fs.writeFileSync(filePath, content);

  console.log(`Saved content to ${filePath}`);
}
    
    
    return `${sanitizedTitle}.md`;
  } catch (error) {
    console.error('获取全文或者生成md文件失败:', error);
    throw error;
  }
}

async function unstarArticle(sessionId, articleId) {

  const response = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'updateArticle',
      sid: sessionId,
      article_ids: articleId,  
      mode: 0,
      field: 0
    })
  });
  
  const data = await response.json();

  return data;
}

async function loginAndGetStarred() {

  const loginRes = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'login',
      user: 'admin',
      password: 'a881018'
    })
  });

  const loginData = await loginRes.json();

  const sessionId = loginData.content.session_id;

  const starredRes = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST',
    body: JSON.stringify({
      op: 'getHeadlines',
      sid: sessionId,
      feed_id: '-1'
    })
  });

  const starredData = await starredRes.json();

for (let article of starredData.content) {
  console.log(article.id);
  console.log(article.title);
  console.log(article.link);
  console.log(article);
  console.log(article.feed_title);
  try {

      await fetchAndSaveMarkdown(article.link, article.feed_title, article.id, sessionId, article.title);
      await unstarArticle (sessionId, article.id);
      
  } catch (error) {
    console.error('Error processing RSS feed:', error);
  }
  }

}

loginAndGetStarred();