---
thumbnail: https://wrhsd001.588886.xyz/thumbnails/09a20d78a093c1f5e5b13a11888b9b7e.png
description: 这篇报道主要内容是：美国拜登政府计划扩大对军事基地附近房地产交易的审查范围，以应对中国带来的潜在威胁。美国财政部提议，根据相关法律，美国外国投资委员会（CFIUS）将对50多处军事基地附近的房地产交易进行审查，并可能阻止一些敏感设施100英里范围内的交易。美国财政部长耶伦表示，此举旨在维护国家安全。  评论：该报道虽提及中国威胁，但更多聚焦于美国政府的政策调整。报道提及中国威胁，反映出美国对中国日益加剧的担忧，这并非毫无依据，中美之间确实存在复杂而激烈的竞争关系，美国希望通过扩大审查范围来保护其军事设施的安全，这一行为也反映了当前中美关系的紧张状态。然而，该报道也体现出西方媒体常见的一种倾向，即强调中国威胁论，以吸引眼球和激发讨论。报道中并未详细说明中国可能对美国军事基地造成的具体威胁，而直接将中国威胁与美国政府的政策调整联系起来，可能有以偏概全之嫌。此外，美国政府此举也可能影响正常的商业交易和民间交流，其实际效果有待进一步观察。
tags: ["2024年07月"]
categories: ["2024年第28周"]
title: 华尔街日报-担忧中国威胁美国计划扩大审查军事基地附近的房地产交易
date: 2024-07-09T00:42:06.489Z
---


<?xml encoding="UTF-8"><html><body><main role="main">
<div>

  

</div>
<div itemprop="articleLead">
    <div>
      <div>
      
          <figure>
    
      
      <div>
        <img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/c45314acbc6a1f432e21c420f65319d7f254cf24" layout="responsive" placeholder alt="" referrerpolicy="no-referrer" loading="lazy">
        
      </div>
    
      <figcaption>
        <p>&#31185;&#24503;&#35282;&#32852;&#21512;&#22522;&#22320;&#12290;&#26681;&#25454;&#19968;&#39033;&#26032;&#25552;&#26696;&#65292;&#22806;&#22269;&#20154;&#22312;&#35813;&#22522;&#22320;&#26041;&#22278;100&#33521;&#37324;&#20869;&#25317;&#26377;&#30340;&#25151;&#20135;&#21487;&#33021;&#23558;&#21463;&#21040;&#20005;&#26684;&#23457;&#26597;&#12290;</p>
    <p> &#22270;&#29255;&#26469;&#28304;&#65306;STEVE HEASLIP/USA TODAY NETWORK/via REUTERS</p>
  </figcaption>
</figure>

      
      
      
      
      
      
      
      
      
      
      
      
      
      
          <!-- eventually when we know what this card will be we can change it and leave this one -->
      
      
         
      
      
      
      </div>
    </div>
</div>
<div>

<div>

  <div>
  
  
      <p> </p>
              <p><span itemprop="name">
                <a href="https://www.wsj.com/news/author/richard-vanderford" itemprop="url" rel="noopener noreferrer" target="_blank">Richard Vanderford</a>
              </span></p>

  </div>
    <time>
      2024&#24180;7&#26376;9&#26085;07:41 CST
    </time>
</div>

<div subscriptions-actions subscriptions-display="NOT data.noSharing">
  <div>
    
  </div>
</div>






</div>
      </main></body></html>
