#!/bin/bash

# Vercel Build Script for <PERSON> with URL Replacement
# This script is optimized for Vercel's build environment

set -e  # Exit on any error

echo "[INFO] Starting Vercel build process..."

# Install npm dependencies
echo "[INFO] Installing npm dependencies..."
npm install

# Install postcss-cli globally
echo "[INFO] Installing postcss-cli..."
npm i -g postcss-cli

# Build Hugo site
echo "[INFO] Building Hugo site..."
hugo --minify

# Check if public directory exists
if [ ! -d "public" ]; then
    echo "[ERROR] Public directory not found after <PERSON> build"
    exit 1
fi

# Run URL replacement (without requiring pip/toml)
echo "[INFO] Starting URL replacement..."
python3 url_replacer.py --config config.toml --public public

if [ $? -ne 0 ]; then
    echo "[ERROR] URL replacement failed"
    exit 1
fi

echo "[INFO] Build completed successfully!"

# Show some statistics
if [ -d "public" ]; then
    file_count=$(find public -type f | wc -l)
    echo "[INFO] Generated site contains $file_count files"
fi
