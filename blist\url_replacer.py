#!/usr/bin/env python3
"""
URL Replacer for Hugo Site
This script replaces URLs in the generated public folder based on config.toml settings.
"""

import os
import re
import argparse
from pathlib import Path
import mimetypes

# Try to import toml, if not available, use a simple parser
try:
    import toml
    HAS_TOML = True
except ImportError:
    HAS_TOML = False

def parse_toml_simple(content):
    """Simple TOML parser for basic key-value pairs and arrays"""
    config = {'params': {}}
    current_section = None

    for line in content.split('\n'):
        line = line.strip()
        if not line or line.startswith('#'):
            continue

        # Section headers
        if line.startswith('[') and line.endswith(']'):
            current_section = line[1:-1]
            if current_section not in config:
                config[current_section] = {}
            continue

        # Key-value pairs
        if '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()

            # Remove quotes
            if value.startswith('"') and value.endswith('"'):
                value = value[1:-1]
            elif value.startswith("'") and value.endswith("'"):
                value = value[1:-1]

            # Handle arrays
            if value.startswith('[') and value.endswith(']'):
                # Simple array parsing
                array_content = value[1:-1].strip()
                if array_content:
                    items = []
                    for item in array_content.split(','):
                        item = item.strip()
                        if item.startswith('"') and item.endswith('"'):
                            item = item[1:-1]
                        elif item.startswith("'") and item.endswith("'"):
                            item = item[1:-1]
                        items.append(item)
                    value = items
                else:
                    value = []

            if current_section:
                config[current_section][key] = value
            else:
                config[key] = value

    return config

def load_config(config_path):
    """Load configuration from config.toml"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if HAS_TOML:
            config = toml.loads(content)
        else:
            config = parse_toml_simple(content)

        params = config.get('params', {})
        inuse_url = params.get('inuse_url', '')
        need_replace_urls = params.get('need_replace_urls', [])

        if not inuse_url:
            print("Warning: inuse_url not found in config.toml")
            return None, None

        if not need_replace_urls:
            print("Warning: need_replace_urls not found in config.toml")
            return None, None

        return inuse_url, need_replace_urls

    except Exception as e:
        print(f"Error loading config: {e}")
        return None, None

def is_text_file(file_path):
    """Check if a file is a text file that should be processed"""
    # Get file extension
    _, ext = os.path.splitext(file_path.lower())
    
    # Define text file extensions that should be processed
    text_extensions = {
        '.html', '.htm', '.css', '.js', '.json', '.xml', '.txt', 
        '.md', '.svg', '.rss', '.atom', '.sitemap'
    }
    
    if ext in text_extensions:
        return True
    
    # Use mimetypes to check if it's a text file
    mime_type, _ = mimetypes.guess_type(file_path)
    if mime_type and mime_type.startswith('text/'):
        return True
    
    return False

def replace_urls_in_file(file_path, inuse_url, need_replace_urls):
    """Replace URLs in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Replace each URL in need_replace_urls with inuse_url
        for old_url in need_replace_urls:
            # Remove trailing slash from old_url for consistent matching
            old_url_clean = old_url.rstrip('/')
            inuse_url_clean = inuse_url.rstrip('/')
            
            # Replace both with and without trailing slash
            content = content.replace(old_url_clean + '/', inuse_url_clean + '/')
            content = content.replace(old_url_clean, inuse_url_clean)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
    
    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return False

def process_directory(directory, inuse_url, need_replace_urls):
    """Process all files in a directory recursively"""
    processed_files = 0
    modified_files = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            
            # Skip if not a text file
            if not is_text_file(file_path):
                continue
            
            processed_files += 1
            
            # Replace URLs in the file
            if replace_urls_in_file(file_path, inuse_url, need_replace_urls):
                modified_files += 1
                print(f"Modified: {file_path}")
    
    return processed_files, modified_files

def main():
    parser = argparse.ArgumentParser(description='Replace URLs in Hugo generated site')
    parser.add_argument('--config', default='config.toml', help='Path to config.toml file')
    parser.add_argument('--public', default='public', help='Path to public directory')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    
    args = parser.parse_args()
    
    # Check if config file exists
    if not os.path.exists(args.config):
        print(f"Error: Config file {args.config} not found")
        return 1
    
    # Check if public directory exists
    if not os.path.exists(args.public):
        print(f"Error: Public directory {args.public} not found")
        return 1
    
    # Load configuration
    inuse_url, need_replace_urls = load_config(args.config)
    if not inuse_url or not need_replace_urls:
        return 1
    
    print(f"Configuration loaded:")
    print(f"  Target URL: {inuse_url}")
    print(f"  URLs to replace: {need_replace_urls}")
    print(f"  Public directory: {args.public}")
    
    if args.dry_run:
        print("\n--- DRY RUN MODE ---")
        print("No files will be modified")
    
    print(f"\nProcessing files in {args.public}...")
    
    if not args.dry_run:
        processed_files, modified_files = process_directory(args.public, inuse_url, need_replace_urls)
        print(f"\nCompleted:")
        print(f"  Processed files: {processed_files}")
        print(f"  Modified files: {modified_files}")
    else:
        # For dry run, just count files that would be processed
        count = 0
        for root, dirs, files in os.walk(args.public):
            for file in files:
                file_path = os.path.join(root, file)
                if is_text_file(file_path):
                    count += 1
        print(f"\nWould process {count} text files")
    
    return 0

if __name__ == '__main__':
    exit(main())
