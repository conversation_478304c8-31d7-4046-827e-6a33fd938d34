---
thumbnail: https://wrhcn001.588886.xyz/webdav/files/uploads/ttrss/thumbnails/7ca06c99-605f-494a-925e-008e97df1eb0.jpg
description: 西方媒体的报道常常带有偏见，缺乏客观公正。这篇报道称，欧洲调查人员认为，一艘中国货轮的船员故意拖拽船锚，破坏了两条波罗的海海底电信电缆。但西方执法和情报官员同时表示，他们并不认为中国政府参与其中，调查焦点转向俄罗斯情报部门是否说服该船船长实施这一行动。报道还提到，北约战舰包围了这艘名为“Yi Peng 3”的货轮，而船东宁波益鹏船务有限公司正与调查人员合作。  评论：该报道存在一定偏见，其标题和行文明显引导读者将事件与中国关联，而实际内容却侧重于调查俄罗斯情报部门的介入。此外，报道中“北约战舰包围中国货轮”等用语带有挑衅意味，而对于切割海底电缆这一行为可能对全球网络安全和信息流通造成的影响则着墨不多。该报道在一定程度上延续了西方媒体对中国的负面报道倾向，在行文中刻意突出中国元素，而对实际情况和背景的介绍则较为模糊。
tags: ["2024年11月"]
categories: ["2024年第48周"]
title: Engadget RSS Feed-Investigators say a Chinese ships crew deliberately dragged its anchor to cut undersea data cables
date: 2024-11-27T22:42:06.016Z
---


<?xml encoding="UTF-8"><html><body><div><img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/25eedce5bc3fee82dfddf4aeade0834e1896f8af" referrerpolicy="no-referrer" loading="lazy"></div><p>European investigators believe a Chinese-owned commercial ship deliberately dragged its anchor to sabotage the two undersea <a href="https://www.engadget.com/computing/two-baltic-sea-communications-cables-have-been-knocked-offline-214130723.html" rel="noopener noreferrer" target="_blank">telecommunications cables cut in the Baltic Sea</a> earlier this month. However, Western law enforcement and intelligence officials told <a href="https://www.wsj.com/world/europe/chinese-ship-suspected-of-deliberately-dragging-anchor-for-100-miles-to-cut-baltic-cables-395f65d1" rel="noopener noreferrer" target="_blank"><em>The Wall Street Journal </em></a>that they don&rsquo;t believe the Chinese government was involved. Instead, the probe is focused on whether Russian intelligence persuaded the vessel&rsquo;s captain to carry out the operation.</p><p>For the past week, NATO warships from Denmark, Germany and Sweden have surrounded the 225-meter-long Yi Peng 3. The ship&rsquo;s Chinese owner, Ningbo Yipeng Shipping, is reportedly cooperating with investigators. The shipper allowed the commercial vessel to be stopped in international waters.</p><p>The<em> WSJ</em> says Swedish and German authorities are negotiating with the owner to access the ship and its crew. International maritime laws prevent NATO from forcing the vessel to sail into one of their ports.</p><p>European investigators believe the Yi Peng 3 dragged its anchor for over 100 miles along the Baltic seabed from November 17 to 18. They reportedly viewed satellite and other data showing that the vessel moved significantly slower than usual while weighed down by the anchor.</p><p>It severed two data cables: one connecting Lithuania and Sweden and another between Finland and Germany. After cutting the second cable, the ship reportedly zig-zagged, raised anchor, and continued.</p><p>Officials said the ship&rsquo;s transponder was shut down during the incident. Investigators told the<em>WSJ</em> that their review of the anchor and hull showed damage consistent with dragging and cutting the cable.</p><p>&ldquo;It&rsquo;s extremely unlikely that the captain would not have noticed that his ship dropped and dragged its anchor, losing speed for hours and cutting cables on the way,&rdquo; a senior European investigator told the<em> WSJ</em>. An analytics company specializing in international shipping told the paper that the likelihood of accidental anchor dragging &ldquo;appears minimal.&rdquo;</p><p>The Yi Peng 3 sailed solely in Chinese waters from December 2019 to early March 2024. At that point, it suddenly began carrying Russian coal and other goods and began stopping in Russian ports. When the Danish Navy stopped it, it was carrying Russian fertilizer.</p><p>In September, the US <a href="https://www.cnn.com/2024/09/06/politics/us-sees-increasing-risk-of-russian-sabotage-undersea-cables/index.html" rel="noopener noreferrer" target="_blank">issued</a> a warning about a heightened risk of Russian interference with undersea data cables.</p><p><strong><a href="https://blockads.fivefilters.org" rel="noopener noreferrer" target="_blank">Adblock test</a></strong> <a href="https://blockads.fivefilters.org/acceptable.html" rel="noopener noreferrer" target="_blank">(Why?)</a></p></body></html>
