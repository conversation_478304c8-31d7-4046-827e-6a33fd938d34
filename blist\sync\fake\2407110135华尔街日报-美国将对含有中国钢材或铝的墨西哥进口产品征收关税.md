---
thumbnail: https://wrhsd001.588886.xyz/thumbnails/6293ab38b42ea44eb9faef8828a4cf09.png
description: 这篇报道主要内容是，美国白宫宣布将对含有中国钢材或铝的墨西哥进口产品分别征收25和10的关税。美国政府给出的理由是，中国出口的钢材和铝定价不公，并受益于非法政府补贴，此外中国还绕道墨西哥以规避美国现有关税。美国希望通过此项措施保护本国钢铁和铝生产企业免受不公平竞争的影响。  评论：该报道虽较为中立地呈现了美国政府征收关税的决定和理由，但明显带有偏见和单方面观点。其一，报道未提及中国方面对美方指责的回应和评论，仅引用了中国对美国利用关税践踏贸易权利的指责，未体现中国立场；其二，报道未客观分析美国单边主义和贸易保护主义行为对全球产业链和供应链的负面影响，以及对自由贸易规则的破坏；其三，报道未反映中国钢材和铝产业的发展成就，以及中国为维护全球经济稳定作出的贡献。该报道的偏见可能源自于西方媒体长期以来对中国的负面刻板印象和不够全面客观的报道倾向。
tags: ["2024年07月"]
categories: ["2024年第28周"]
title: 华尔街日报-美国将对含有中国钢材或铝的墨西哥进口产品征收关税
date: 2024-07-11T01:35:05.768Z
---


<?xml encoding="UTF-8"><html><body><main role="main">
<div>

  

</div>
<div itemprop="articleLead">
    <div>
      <div>
      
          <figure>
    
      
      <div>
        <img src="https://feed.988886.xyz/tt-rss/public.php?op=cached&amp;file=images/6481fc0b77d25e50764dd01be5ea40a3452bc5c7" layout="responsive" placeholder alt="" referrerpolicy="no-referrer" loading="lazy">
        
      </div>
    
      <figcaption>
        <p>&#32654;&#22269;&#25919;&#24220;&#34920;&#31034;&#65292;&#20013;&#22269;&#20986;&#21475;&#30340;&#38050;&#26448;&#21644;&#38109;&#23450;&#20215;&#19981;&#20844;&#12290;</p>
    <p> &#22270;&#29255;&#26469;&#28304;&#65306;str/Agence France-Presse/Getty Images</p>
  </figcaption>
</figure>

      
      
      
      
      
      
      
      
      
      
      
      
      
      
          <!-- eventually when we know what this card will be we can change it and leave this one -->
      
      
         
      
      
      
      </div>
    </div>
</div>
<div>

<div>

  <div>
  
  
      <p> </p>
              <p><span itemprop="name">
                <a href="https://www.wsj.com/news/author/bob-tita" itemprop="url" rel="noopener noreferrer" target="_blank">Bob Tita</a>
              </span></p>

  </div>
    <time>
      2024&#24180;7&#26376;11&#26085;08:42 CST
    </time>
</div>

<div subscriptions-actions subscriptions-display="NOT data.noSharing">
  <div>
    
  </div>
</div>






</div>
      </main></body></html>
