#!/bin/bash
msg="rebuilding site $(date)"

cd /root/blist/auto-rss && /usr/bin/node starred-go.js && /usr/bin/node pubilshed.js && /usr/bin/rclone move /root/blist/auto-rss/draft jianguo:Mac/Documents/档案/Obsidian/huan/ssn/草稿 && /usr/bin/rclone move /root/blist/auto-rss/fake jianguo:Mac/Documents/档案/Obsidian/huan/ssn/web-db-new && /usr/bin/rclone move /root/blist/auto-rss/sci jianguo:Mac/Documents/档案/Obsidian/huan/ssn/web-sci && /usr/bin/rclone move /root/blist/auto-rss/web jianguo:Mac/Documents/档案/Obsidian/huan/ssn/web-web && /usr/bin/rclone move /root/blist/auto-rss/txt /root/blist/content/de/draft && cd /root/blist-hugo && /bin/bash /root/blist-hugo/blist-hugo.sh