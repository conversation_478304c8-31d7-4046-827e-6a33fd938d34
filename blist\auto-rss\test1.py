from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import <PERSON><PERSON><PERSON><PERSON>

def check_ssl(urls, max_attempts):
    # 设置Chromium选项
    chrome_options = Options()
    chrome_options.binary_location = '/snap/bin/chromium'  # 更新为你的chromium浏览器的路径
    chrome_options.add_argument("--headless")  # 确保在后台运行
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")


    # 设置webdriver服务
    service = Service('/usr/bin/chromedriver')  # 更新为你的chromedriver的路径

    # 创建一个新的浏览器实例
    driver = webdriver.Chrome(service=service, options=chrome_options)

    for url in urls:
        attempts = 0
        while attempts < max_attempts:
            driver.get(url)
            status_code = driver.execute_script("return document.readyState;")
            if status_code == 'complete':
                print(f"页面加载完成: {url}")
                break
            else:
                print(f"收到错误码200: {url}")
                attempts += 1
        if attempts == max_attempts:
            print(f"尝试{max_attempts}次后，仍然收到错误码200: {url}")

    # 关闭浏览器
    driver.quit()

# 使用示例
urls = ["https://news.wrss.tk/"]
max_attempts = 5
check_ssl(urls, max_attempts)
