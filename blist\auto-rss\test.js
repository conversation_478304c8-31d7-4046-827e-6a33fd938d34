const { parseString } = require('xml2js');
exports.name = 'pythonclaude2';
const axios = require('axios');
const fs = require('fs');

const path = require('path');
const { promisify } = require('util');
const http = require('http');
const https = require('https');
const util = require('util');
const querystring = require('querystring');

const cheerio = require('cheerio');

const sanitizeFilename = require('sanitize-filename'); // You may need to install this package
const { exec } = require('child_process');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

const statAsync = util.promisify(fs.stat);
const unlinkAsync = promisify(fs.unlink);

const tokenFilePath = path.join(__dirname, 'kathystevens5.token');
let accessToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
//console.log(accessToken);

const username = '<EMAIL>'; // 替换成实际的用户名
const password = '#P#82zT#hs'; // 替换成实际的密码
//const baseUrl = 'https://api.wrss.tk';
const baseUrl = 'http://20.89.50.152:7999';

const renewAccessToken = async (email, password) => {
    const loginUrl = `${baseUrl}/auth/token`;
    const data = querystring.stringify({
      username,
      password,
    });

    let attempts = 0;
    while (attempts < 2) {
      try {
        const response = await axios.post(loginUrl, data, {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });
        const accessToken = response.data.accessToken;
        const session_token = response.data.session_token;
    console.log('accessToken已保存:', response.data.accessToken);
    fs.writeFileSync(tokenFilePath, response.data.accessToken, 'utf-8');
    return accessToken;
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed with error:`, error);
        attempts++;
        if (attempts < 2) {
          await new Promise((resolve) => setTimeout(resolve, 3000)); // 等待3秒
        } else {
          throw error; // 如果重试5次依然报错的话，再抛出错误
        }
      }
    }
  };
  
renewAccessToken()