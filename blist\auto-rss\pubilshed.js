
const xml2js = require('xml2js');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const cheerio = require('cheerio');

const targetTime = new Date("2023-07-31T00:14:07+00:00");
const rssUrl = "https://feed.988886.xyz/tt-rss/public.php?op=rss&id=-2&is_cat=0&q=&key=9fb2j66374c56736856";

const entryIdFilePath = path.join(__dirname, 'news-entryId.id');
let processedEntryIds = [];

try {
  if (!fs.existsSync(entryIdFilePath)) {
    fs.writeFileSync(entryIdFilePath, ''); // 创建空文件
    console.log('Created empty entryId file.');
  }
  
  const entryIdData = fs.readFileSync(entryIdFilePath, 'utf-8');
  processedEntryIds = entryIdData.split('\n').filter(Boolean);
} catch (error) {
  console.error('Error reading entryId file:', error);
}


async function unpublishArticle(sessionId, articleId) {
  const response = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'updateArticle',
      sid: sessionId,
      article_ids: articleId,  
      mode: 0,
      field: 1
    })
  });  
  const data = await response.json();
  return data;
}

async function unpublishAllArticles() {
  const loginRes = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'login',
      user: 'admin',
      password: 'a881018'
    })
  });
  const loginData = await loginRes.json();
  const sessionId = loginData.content.session_id;	
  // 第一步:获取所有已发布文章
  const publishedRes = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'getHeadlines',
      sid: sessionId,
      feed_id: '-2' // feed_id -2 是已发布文章
    })
  });
  
  const publishedArticles = await publishedRes.json();
  // 第二步:循环调用 unpublish 取消发布
  for (let article of publishedArticles.content) {
    await unpublishArticle(sessionId, article.id);  
  }
}

async function uploadImageToLsky222222(imageUrl) { //222222 原来的
    const baseUrl = 'https://tu.988886.xyz/api/v1';
    const uploadEndpoint = '/upload';   
    const token = '3|z8icUcKgak6R52f5NF4ymGJqd8cGT43LD5R23FeX';
    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
    };

    // 下载图片
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageData = Buffer.from(imageResponse.data, 'binary');
    
    // 构建FormData
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', imageData, 'image.png');
    
    try {
        // 上传图片
        const response = await axios.post(baseUrl + uploadEndpoint, form, { headers });

        if (response.status === 200) {
            const responseData = response.data;
            if (responseData.status) {
                const links = responseData.data.links;
                let url = links.url;
                let thumbnailUrl = links.thumbnail_url;
                
                // 替换URL中的特定部分
                url = url.replace('https://tu.988886.xyz/', 'https://wrhsd001.588886.xyz/');
                thumbnailUrl = thumbnailUrl.replace('https://tu.988886.xyz/', 'https://wrhsd001.588886.xyz/');                
                return { url, thumbnailUrl };
            } else {
                throw new Error('Image upload failed. Message: ' + responseData.message);
            }
        } else {
            throw new Error('Image upload request failed. Status code: ' + response.status);
        }
    } catch (error) {
        throw error;
    }
}

async function uploadImageToLsky(imageUrl) {
    const baseUrl = 'https://xrea.988886.xyz/webdav';
    const uploadEndpoint = '/fixed-upload-new.php';   
    const headers = {
        'Content-Type': 'multipart/form-data',
    };

    // 下载图片
    const imageResponse = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const imageData = Buffer.from(imageResponse.data, 'binary');
    
    // 构建FormData
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', imageData, 'image.jpg');
    form.append('thumbnail', 'true');
    form.append('original', 'false');
    form.append('path', 'ttrss');
    
    try {
        // 上传图片
        const response = await axios.post(baseUrl + uploadEndpoint, form, { headers: { ...headers, ...form.getHeaders() } });

        if (response.status === 200) {
            const responseData = response.data;
            const thumbnailUrl = responseData.thumbnailResult;

            return { thumbnailUrl };
        } else {
            throw new Error('Image upload request failed. Status code: ' + response.status);
        }
    } catch (error) {
        throw error;
    }
}


async function fetchAndSaveMarkdown(url, title) {
  try {
//保存txt文件
    const txtdecodedText = url.replace(/&amp;/g, '&');
    const txtresponse = await fetch(txtdecodedText);    
    const htmlContent = await txtresponse.text();
    const $ = cheerio.load(htmlContent);
    const txtsanitizedTitle = title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]+/g, '-');
    const txtarticleContent = $('article').text();
    const txtmarkdownText = `# ${txtsanitizedTitle}\n\n${txtarticleContent}`;
    const txtoutputPath = path.join(__dirname, "txt", `${txtsanitizedTitle}.md`);
    await fs.promises.writeFile(txtoutputPath, txtmarkdownText);
    console.log(`Markdown file saved as ${txtsanitizedTitle}.txt`);
    
    const encodedURL = encodeURIComponent(url);
    const apiUrl = `https://quanwen.988886.xyz/parser?url=${encodedURL}`;
    const response = await axios.get(apiUrl);
    const articleData = response.data;
    console.log(articleData); 
    let thumbnailUrl = 'https://wrhsd001.588886.xyz/thumbnails/cb6b93424cdac6d087679838f0b18e3f.png'; // Default thumbnail URL
    if (articleData.lead_image_url) {
      console.log('Original thumbnail URL:', articleData.lead_image_url); // Debug

      //const newImageUrl = await uploadImageToTelegraph(articleData.lead_image_url);.
      const newImageUrldata = await uploadImageToLsky(articleData.lead_image_url); //修改为使用wwxA1上的蓝空图床
      //const newImageUrl = newImageUrldata.url;
      const newImageUrl = newImageUrldata.thumbnailUrl; //修改为使用缩略图
      console.log('New thumbnail URL:', newImageUrl); // Debug

      if (newImageUrl) {
        thumbnailUrl = newImageUrl;
      }
    }    
    return thumbnailUrl;
  } catch (error) {
    console.error('获取全文缩略图失败:', error);
    throw error;
  }
}

function decodeHtmlCharCodes(str) {
  // 检查str是否未定义或不是一个字符串
  if (typeof str !== 'string') {
    // 返回原始输入或空字符串，根据你的需求选择
    return str || '';
  }

  return str.replace(/&#x([a-fA-F0-9]+);/g, function(match, dec) {
    return String.fromCharCode(parseInt(dec, 16));
  });
}

const { JSDOM } = require("jsdom");

// 确保JSDOM已经被定义
if (typeof JSDOM !== "undefined") {
  function extractTextFromHTML(htmlString) {
    // 检查htmlString是否为字符串
    if (typeof htmlString !== 'string') {
      // 如果不是字符串，返回空字符串或其他适当的默认值
      return '';
    }

    const dom = new JSDOM(htmlString);
    const document = dom.window.document;
    const window = dom.window;
    let text = "";
    const walk = document.createTreeWalker(document.body, window.NodeFilter.SHOW_TEXT, null, false);
    while(walk.nextNode()) {
      text += walk.currentNode.textContent + ' '; // 使用空格而不是换行符连接文本
    }
    return text.trim(); // 移除最后的空格
  }
} else {
  console.error("JSDOM is not defined. Please ensure you are running this in a Node.js environment with jsdom installed.");
}

function processEntries(entries) {
  const processEntry = async (entry) => {
    const sourceTitle = entry.source[0].title[0].toString();
    const entryTitle = entry.title[0]._ ? entry.title[0]._ : entry.title[0].toString();
    const entryTime = new Date(entry.source[0].updated[0]);
    const entryId = entry.id[0].split("/")[1]; 
    const entrytag = entry.id[0].split(":/")[0];
    //console.log(entrytag);
    const publishdate = entrytag.split(",")[1];
    //console.log(publishdate);
    const modtime = "00:00:00";
    const isoDateTime = `${publishdate}T${modtime}+00:00`;
//跳过已保存的    
  //  if (processedEntryIds.includes(entryId)) {
    //  console.log(`Skipping already processed entry: ${entryId}`);
     // return;
   // }
    
    const entryData = {
      sourceTitle,
      entryTitle,
      entryTime,
      link: entry.link[0].$.href
    };

    console.log(entryData.link); // Debug
    console.log(entryId);

    const newTitle = `${sourceTitle}-${entryTitle}`.replace(/[^\w\u4e00-\u9fa5\s-]/g, '');
    const sanitizedTitle = `${sourceTitle}-${entryTitle}`.replace(/[^\w\u4e00-\u9fa5]/g, '-');
//用entryId到ttrss直接获取labels
  const loginRes = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST', 
    body: JSON.stringify({
      op: 'login',
      user: 'admin',
      password: 'a881018'
    })
  });
  const loginData = await loginRes.json();
  const sessionId = loginData.content.session_id;	
  const rssres = await fetch('https://feed.988886.xyz/tt-rss/api/', {
    method: 'POST',
    body: JSON.stringify({
      op: 'getArticle',
      sid: sessionId,
      article_id: entryId
    })  
  });

  const rssdata = await rssres.json();
  const article = rssdata.content[0];
  //const content1 = article.content;    
  const content0 = article.content;
  const content1 = content0.replace(/https:\/\/ifeed\.eu\.org\/tt-rss\/public\.php\?op=cached/g, 'https://wrh.588886.xyz/tt-rss/public.php?op=cached');

      const decodedContent1 = decodeHtmlCharCodes(content1);
//      console.log('decodedContent1:', decodedContent1);
      const extractedText1 = extractTextFromHTML(decodedContent1);
     console.log('extractedText1:', extractedText1);
  
    //这里是将ttrss的缓存的图片用cachefly进行cdn加速
    const decodedText = entryData.link.replace(/&amp;/g, '&');    
    const thumbnailurl = await fetchAndSaveMarkdown(decodedText, sanitizedTitle);
    console.log(thumbnailurl);
    const year = entryTime.getFullYear();
    const month = entryTime.getMonth() + 1;

    const currentDate = new Date();
    const startOfYear = new Date(currentDate.getFullYear(), 0, 1); // January 1st of the current year
    const daysPassed = Math.floor((entryTime - startOfYear) / (24 * 60 * 60 * 1000)) + 1;
    const week = Math.ceil(daysPassed / 7);   

//文件名 插入时间
const datePublished = entryTime;

const titleyear = datePublished.getFullYear().toString().substr(2, 2); // 获取年份的后两位
const titlemonth = (datePublished.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
const titleday = datePublished.getDate().toString().padStart(2, '0'); // 获取日期并补零
const titlehours = datePublished.getHours().toString().padStart(2, '0'); // 获取小时并补零
const titleminutes = datePublished.getMinutes().toString().padStart(2, '0'); // 获取分钟并补零

const titledata = `${titleyear}${titlemonth}${titleday}${titlehours}${titleminutes}`;
console.log(titledata); // 输出类似 '2309021900'
    


    const metadata = 
`---
thumbnail: ${thumbnailurl}
tags: ["${year}年${month < 10 ? '0' + month : month}月"]
categories: ["${year}年第${week}周"]
title: ${newTitle}
date: ${entryTime.toISOString()}
---
`;

    const content = entry.content[0]._;
    const markdownContent = `${metadata}\n\n${content1}`;

// 添加标签-文本映射
const labelMap = {
  '': '虽然你身处的环境，或多或少会影响你的心情，但有些事也依然取决于你自己。', // 对应空标签
  'fake': '随手搬运西方主流媒体的所谓的民主自由的报道，让帝国主义的丑恶嘴脸无处遁形。', // 对应 label1 标签
  'sci': '歼灭别有用心的反智、伪科学、带风向言论，人人有责，从我做起。',
  'web': '虽然你身处的环境，或多或少会影响你的心情，但有些事也依然取决于你自己。', // 对应 label2 标签
  // 你可以根据需要添加更多的标签-文本映射
};

if (!article.labels || article.labels.length === 0) {
  // 如果 labels 为空或没有内容，则保存到 draft 文件夹
  const fileNamess = `${titledata}${sanitizedTitle}.md`.replace(/ /g, "_");
  saveContent(article, `${sanitizedTitle}.md`, 'draft', markdownContent, labelMap['']);
  saveContent(article, fileNamess, 'publishembd', extractedText1, labelMap['']);
} else {
  // 如果 labels 不为空
  for (const label of article.labels) {
    const labelName = label[1]; // 获取 label 的名称
    const fileNamesss = `${titledata}${sanitizedTitle}.md`.replace(/ /g, "_");
    saveContent(article, `${titledata}${sanitizedTitle}.md`, labelName, markdownContent, labelMap[labelName]);
    saveContent(article, fileNamesss, 'publishembd', extractedText1, labelMap[labelName]);
  }
}
    processedEntryIds.push(entryId);
  };
  const processNextEntry = async (index) => {
    if (index < entries.length) {
      await processEntry(entries[index]);
      await processNextEntry(index + 1);
    } else {
      // All entries processed, write updated entry IDs back to file
      fs.writeFileSync(entryIdFilePath, processedEntryIds.join('\n'));
      console.log(`Updated entry IDs saved to: ${entryIdFilePath}`);
    }
  };
  return processNextEntry(0);
}

function saveContent(article, fileName, directory, content, desc) {
  const savePath = path.join(__dirname, directory);
  
  // 创建保存目录（如果不存在）
  if (!fs.existsSync(savePath)) {
    fs.mkdirSync(savePath);
  }

  // 检查 article.note 是否有内容，没有则使用标签对应的文本
  desc = article.note || desc;

  // 保存内容到文件

  const filePath = path.join(savePath, fileName);
  
  // 在内容的第二行插入描述
  const contentLines = content.split('\n');
  contentLines.splice(2, 0, `description: ${desc}`);
  const newContent = contentLines.join('\n');
  fs.writeFileSync(filePath, newContent);
  console.log(`Saved content to ${filePath}`);
}  

fetch(rssUrl)
  .then(response => response.text())
  .then(xmlData => {
    const parser = new xml2js.Parser();
    parser.parseString(xmlData, (error, result) => {
      if (error) {
        console.error("Error parsing XML:", error);
        return;
      }

      if (!result.feed.entry || result.feed.entry.length === 0) {
        console.log("No articles found.");
        return;
      }

      const entries = result.feed.entry.filter(entry => {
        const entryTime = new Date(entry.source[0].updated[0]);
        return entryTime > targetTime;
      });

      if (entries.length === 0) {
        console.log("No articles published after the target time.");
        return;
      }

      processEntries(entries)
        .then(() => {
          console.log("All links fetched and saved.");
          unpublishAllArticles();  
        })
        .catch(error => {
          console.error("Error fetching and processing entries:", error);
        });
    });
  })
  .catch(error => {
    console.error("Error fetching RSS feed:", error);
  });