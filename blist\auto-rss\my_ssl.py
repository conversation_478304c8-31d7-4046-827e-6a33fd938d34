import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# 设置重试策略，最大重试次数为3次
retry_strategy = Retry(
    total=3,
    backoff_factor=1,
    status_forcelist=[502, 503, 504],
)

# 创建一个带有重试策略的HTTP适配器
adapter = HTTPAdapter(max_retries=retry_strategy)

# 创建一个Session，使用上述适配器
session = requests.Session()
session.mount('http://', adapter)
session.mount('https://', adapter)


# 要检查的多个网站URL列表
urls = ['https://news.wrss.tk/', 'https://news.wrss.tk/fake/', 'https://news.wrss.tk/sci/', 'https://news.wrss.tk/web/', 'https://news.wrss.tk/wechat/', 'https://news.wrss.tk/tags/', 'https://news.wrss.tk/categories/', 'https://news.wrss.tk/draft/', 'https://news.wrss.tk/onepage/']

# 遍历网址列表并检查SSL证书，然后模拟访问
for url in urls:
    print(f"检查 {url} 的SSL证书...")
    try:
        response = session.get(url, verify=True)
        response.raise_for_status()  # 检查HTTP响应状态码
        print(f"{url} SSL证书正常，模拟访问中...")
        
        # 模拟访问，触发CDN缓存页面资源
        try:
            response = session.get(url)
            response.raise_for_status()  # 检查HTTP响应状态码
            print(f"{url} 模拟访问成功！")
        except requests.exceptions.RequestException as e:
            print(f"访问错误: {e}")
            
    except requests.exceptions.SSLError as e:
        print(f"SSL证书错误: {e}")
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")

# 在此之后，您可以添加您要执行的其他操作