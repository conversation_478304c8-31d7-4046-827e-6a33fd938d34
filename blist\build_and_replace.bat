@echo off
setlocal enabledelayedexpansion

REM Hugo Build and URL Replace Script for Windows
REM This script builds the Hugo site and then replaces URLs in the generated files

echo [INFO] Starting Hugo build and URL replacement process...

REM Check if <PERSON> is installed
hugo version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Hugo is not installed or not in PATH
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    python3 --version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Python is not installed or not in PATH
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

REM Check if required Python packages are installed
echo [INFO] Checking Python dependencies...
%PYTHON_CMD% -c "import toml" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] toml package not found. Installing...
    %PYTHON_CMD% -m pip install toml
    if errorlevel 1 (
        echo [ERROR] Failed to install toml package
        exit /b 1
    )
)

REM Build the Hugo site
echo [INFO] Building Hugo site...
hugo --minify
if errorlevel 1 (
    echo [ERROR] Hugo build failed
    exit /b 1
)

echo [INFO] Hugo build completed successfully

REM Check if public directory exists
if not exist "public" (
    echo [ERROR] Public directory not found after Hugo build
    exit /b 1
)

REM Run URL replacement
echo [INFO] Starting URL replacement...
%PYTHON_CMD% url_replacer.py --config config.toml --public public
if errorlevel 1 (
    echo [ERROR] URL replacement failed
    exit /b 1
)

echo [INFO] URL replacement completed successfully
echo [INFO] Site build and URL replacement finished!

REM Optional: Show some statistics
if exist "public" (
    for /f %%i in ('dir /s /b public ^| find /c /v ""') do set file_count=%%i
    echo [INFO] Generated site contains !file_count! files
)

pause
