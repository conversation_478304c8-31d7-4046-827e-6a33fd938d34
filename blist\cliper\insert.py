# coding = utf-8

import os
import datetime
import time

path = r'/root/blist/cliper/tiny'
today = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
today1 = time.strftime("%Y年%m月", time.localtime())
today2 = time.strftime("%Y年第%W周", time.localtime())
for root, dirs, files in os.walk(path, topdown=False):
    for name in files:
        _path = os.path.join(root, name)
        _name = name[:len(name)-3]
#        with open(_path, "r+", encoding="gbk") as f:
        with open(_path, 'r+') as f: 
#            old = f.read()
            data = f.readlines()
            data1=data[3:]
            data2=data[1]
            data3=data[2]
            data4=data2.split("](")[0]
            data5=data4[1:len(data4)]
            f.seek(0,0)
            f.write("---\n")
            f.write("thumbnail: %s\n" %data5)
#            f.write("toc: true \n")
            f.write('tags: ["%s"]\n' %today1)
            f.write('categories: ["%s"]\n' %today2)
            f.write("title: %s\n" %_name)
            f.write("date: %s\n" %today)
            f.write("description: 虽然你身处的环境，或多或少会影响你的心情，但有些事也依然取决于你自己。\n")
            f.write("---\n")
            f.writelines(data1)

