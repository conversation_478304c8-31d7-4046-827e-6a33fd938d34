import requests
import time
import json
import os

fastkey = 'fastgpt-fcStpG4tLriopsJddhoBhNzycB70EmcqwhIBXI9vcTC3rw7twn3avHxrFm2std'
datasetId = "662f94f6f00f40f389d62e21"  # Assuming datasetId remains constant

def upload_text_and_check_queue(text, name):
    url = 'http://146.56.99.144:3000/api/core/dataset/collection/create/text'
    headers = {
        'Authorization': f'Bearer {fastkey}',
        'Content-Type': 'application/json'
    }
    data = {
        "text": text,
        "datasetId": datasetId,
        "parentId": None,
        "name": name,
        "trainingType": "chunk",
        "chunkSize": 512,
        "chunkSplitter": "",
        "qaPrompt": "<Context></Context> 标记中是一段文本，学习和分析它，并整理学习成果：\n- 提出问题并给出每个问题的答案。\n- 答案需详细完整，尽可能保留原文描述。\n- 答案可以包含普通文字、链接、代码、表格、公示、媒体链接等 Markdown 元素。\n- 最多提出 30 个问题。\n",
        "metadata": {}
    }
    
    response = requests.post(url, json=data, headers=headers)
    if response.status_code == 200:
        print(f"Text '{name}' uploaded successfully.")
        return True
    else:
        print(f"Failed to upload text '{name}'.")
        return False

def process_markdown_files(folder_paths):
    for folder_path in folder_paths:
        for filename in os.listdir(folder_path):
            if filename.endswith(".md"):
                file_path = os.path.join(folder_path, filename)
                with open(file_path, 'r', encoding='utf-8') as file:
                    text_content = file.read()
                if upload_text_and_check_queue(text_content, filename):
                    os.remove(file_path)
                    print(f"File '{filename}' deleted after successful upload.")
    check_training_queue()

def check_training_queue(start_time=None):
    token = login_and_get_token() 
    if start_time is None:
        start_time = time.time()
    elapsed_time = time.time() - start_time
    if elapsed_time > 3600:  # 3600 seconds
        print('索引超时，终止重试')
        return

    try:
        url = 'http://146.56.99.144:3000/api/core/dataset/training/getQueueLen?vectorModel=text-embedding-ada-002&agentModel=gpt-3.5-turbo'
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Token': token
        }
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            raise Exception(f"HTTP error! status: {response.status_code}")

        queue_json_response = response.json()
        print('Queue Response:', queue_json_response)

        if queue_json_response['code'] == 200:
            vector_training_count = queue_json_response['data']['vectorTrainingCount']
            agent_training_count = queue_json_response['data']['agentTrainingCount']
            if vector_training_count == 0 and agent_training_count == 0:
                print('索引完成')
                return
            else:
                print('索引未完成，正在处理中...')
                time.sleep(1)  # Retry after 1 second
                check_training_queue(start_time)
        else:
            print('测试未成功，响应码非200')
    except Exception as error:
        print('Error:', error)

def login_and_get_token():
    global token
    url = 'http://146.56.99.144:3000/api/support/user/account/loginByPassword'
    headers = {'Content-Type': 'application/json'}
    payload = {
        "username": "root",
        "password": "0dc05a5475a58df7c6ad36eee47310759afb831ca4696d9e0a807e0905a2c991"
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    if response.status_code != 200:
        raise Exception(f"HTTP error! status: {response.status_code}")
    token = response.json()['data']['token']    
    return token

# Example usage
folder_paths = ['/root/blist/auto-rss/publishembd', '/root/blist/auto-rss/starembd']
process_markdown_files(folder_paths)