baseurl = "https://blist-ij2w.vercel.app/"
metaDataFormat = "yaml"
title = "真相集中营"
theme = 'blist'
buildfuture = true 

DefaultContentLanguage = "en"
DefaultContentLanguageInSubdir = true
[languages]
  [languages.en]
    contentDir = "content/en" #English
    weight = 1
    languageName = "English"
    [languages.en.params]
      introTitle = "生命不息，战斗不止！"
      introSubtitle = "抓取外媒涉中的报道，全方位展示西方歪曲事实的手法。时刻警惕：帝国主义亡我之心不死！"
      introPhoto = "https://wrhsd001.588886.xyz/up/2023/11/14/6552dee3d6aed.png"
      logo = "/2.png"
#    [[languages.en.menu.main]]
#        name = "博客"
#        url = "https://tt.wrhcn.tk"
#        weight = 7
        
    [[languages.en.menu.main]]
        name = "灯塔底线"
        url = "fake/"
        weight = 1

    [[languages.en.menu.main]]
        name = "科普常识"
        url = "sci/"
        weight = 2

    [[languages.en.menu.main]]
        name = "网摘"
        url = "web/"
        weight = 3

    [[languages.en.menu.main]]
        name = "公众号订阅"
        url = "newgzh/"
        weight = 4

    [[languages.en.menu.main]]
        name = "OnePage"
        url = "onepage/"
        weight = 5

    [[languages.en.menu.main]]
        name = "月刊"
        url = "tags/"
        weight = 6

    [[languages.en.menu.main]]
        name = "周刊"
        url = "categories/"
        weight = 7
        
    [[languages.en.menu.main]]
        name = "Draft"
        url = "draft/"
        weight = 8


 #   [[languages.en.menu.main]]
 #       name = "Top Post"
 #       url = "/en/blog/emoji-support/"
 #       weight = 4


[params]
  # Enable the darkmode toggle in header
  darkModeToggle = true

  # Enable search in header
  enableSearch = true

  # Custom copyright - optional
  copyright = "© 2024 - HuanGe · All rights reserved"
  favicon = "/favicon-16x16.png"

  # URL replacement configuration
  inuse_url = "https://xrea.988886.xyz/"
  need_replace_urls = [
    "https://wrhcn001.588886.xyz/",
    "https://wrhsd001.588886.xyz/"
  ]

  # Color for the intro details and social links block, not applicable for dark mode
  # Supported values: Any color from TailwindCSS default colors
  # Reference: https://tailwindcss.com/docs/customizing-colors
  ascentColor = "bg-blue-100"

  # The page bundle that is shown on the front page
  #frontBundle = "blog", "sci" 
  frontBundle = "top"

  # Used to hide the post metadata such as posted date, reading time and word count
  # Can be used at site level or page level
  hideMeta = false

  # To hide "Other languages" option if the post is available in multiple languages
  # Can be used at site level or page level
  hideOtherLanguages = false



[[menu.main]]
  name = "About"
  url = "/about"

[build]
  writeStats = true

[outputs]
  home = ["HTML"]

#  home = ["HTML", "RSS", "JSON"]

# syntax highlight settings
[markup]
  [markup.highlight]
    style = "dracula"
  [markup.goldmark.renderer]
    # Enable to include inline HTML
    unsafe = true
