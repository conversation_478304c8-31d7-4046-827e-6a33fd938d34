const { parseString } = require('xml2js');
exports.name = 'pythonclaude2';
const axios = require('axios');
const fs = require('fs');

const path = require('path');
const { promisify } = require('util');
const http = require('http');
const https = require('https');
const util = require('util');
const querystring = require('querystring');

const cheerio = require('cheerio');

const sanitizeFilename = require('sanitize-filename'); // You may need to install this package
const { exec } = require('child_process');


const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);

const statAsync = util.promisify(fs.stat);
const unlinkAsync = promisify(fs.unlink);

const tokenFilePath = path.join(__dirname, 'kathystevens5-.token');
let accessToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
//console.log(accessToken);

const username = '<EMAIL>'; // 替换成实际的用户名
const password = '#P#82zT#hs'; // 替换成实际的密码
const baseUrl = 'https://gpt.wrss.tk';
const basepandoraUrl = 'http://20.89.50.152:8181';



async function checkAccessToken() {
  const config = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessToken}`,
    },
  };

  try {
    const response = await axios.get(`${baseUrl}/backend-api/accounts/check`, config);
    console.log('token正常:', response.data);
    // Process the list of available models as needed
  } catch (error) {
    console.error('账户错误或token过期', error);
  }  
}



async function getResponse() {
  let prompt = '你是谁';
  // 请求数据
  const accessToken = await checkAccessToken();
  
  //await checkAccessTokengo();
  //await new Promise((resolve) => setTimeout(resolve, 10000)); // go 程序获取token需要时间 等待10s
  const gptToken = fs.readFileSync(tokenFilePath, 'utf-8').trim();
  const requestData = {
    action: 'next',
    messages: [
      {
        id: '469d00af-6095-47b2-a9a0-8fec94b175d5', // 请替换成唯一的消息ID
        author: {
          role: 'user'
        },
        content: {
          content_type: 'text',
          parts: [
            prompt
          ]
        },
        metadata: {}
      }
    ],
    model: 'text-davinci-002-render-sha',
    parent_message_id: '8c875ca4-59c4-444d-920a-3a490d1f797d',
    timezone_offset_min: -480,
    history_and_training_disabled: false
  };

  try {    
    const response = await axios.post(`${baseUrl}/backend-api/conversation`, requestData, {
      headers: {
        Authorization: `Bearer ${gptToken}`,
        'Content-Type': 'application/json',
        Accept: 'text/event-stream'
      }
    });
    // 处理响应
    let finishedResponses = [];

    response.data.split('\ndata:').forEach(dataChunk => {
      if (dataChunk.includes('"status": "finished_successfully"') && dataChunk.includes('"is_complete": true') && dataChunk.includes('"role": "assistant"')) {
        const contentMatch = /"content": {"content_type": "text", "parts": \["(.*?)"\]}/.exec(dataChunk);
        if (contentMatch && contentMatch[1]) {
          const content = contentMatch[1].replace(/\\u(\d{4})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16)));
          const decodedContent = JSON.parse('"' + content + '"'); // 解码Unicode转义字符
          finishedResponses.push(decodedContent);
        }
      }
    });
    
    if (finishedResponses.length > 0) {
      text = finishedResponses[0];
    }
    //console.log(text);
  } catch (error) {
    // 处理错误
    console.error('发生错误：', error);
  }
  console.log('返回内容：',text);
  return text; 
}



checkAccessToken();